// Generic outbound agent that handles CSV, API, and XML processing through collection-based handlers
const logger = require('../config/logger');
const PerformanceMonitor = require('../services/performance.service');
const config = require('../config/config');
const {
  setupQueueConsumer,
  getHandler,
  setupGracefulShutdown,
  validateAgentConfig,
  logAgentActivity
} = require('../helpers/agent.helper');

/**
 * Legacy API queue listener (kept for backward compatibility)
 * @param {Object} agent - Agent configuration
 * @param {Function} handlerFunction - Handler function to process messages
 * @param {Object} performanceMonitor - Performance monitor instance
 * @returns {Promise<void>}
 */
async function setupApiQueueListener(agent, handlerFunction, performanceMonitor) {
  try {
    logAgentActivity(agent.name, `Setting up queue listener for: ${agent.queue}`);

    // Shared counters for the session
    let totalMessagesProcessed = 0;
    let successfulMessages = 0;
    let failedMessages = 0;
    let totalApiCalls = 0;
    let validationErrors = [];

    // Message handler function
    const messageHandler = async (msg, channel) => {
      const messageStartTime = Date.now();
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Parse message
      const messageContent = msg.content.toString();
      let event;

      try {
        event = JSON.parse(messageContent);
      } catch (parseError) {
        logger.error(`[${agent.name}] Failed to parse message from queue ${agent.queue}:`, parseError);
        failedMessages++;
        totalMessagesProcessed++;

        // Log parsing error to performance monitor with stack trace
        performanceMonitor.logProgress(`Message parsing failed`, {
          messageId,
          error: parseError,
          totalProcessed: totalMessagesProcessed,
          failed: failedMessages,
          messageContent: messageContent.substring(0, 200) + '...' // First 200 chars for debugging
        });

        channel.ack(msg);
        return;
      }

      logAgentActivity(agent.name, `Processing event from queue ${agent.queue}`, {
        eventType: event.event_type,
        traceId: event.trace_id,
        messageId
      });

      try {
        // Process the event through handler
        const result = await handlerFunction({
          event,
          agent,
          performanceMonitor: performanceMonitor,
          messageId
        });

        // Update counters based on result
        if (result && result.errors && result.errors.length > 0) {
          validationErrors.push(...result.errors);
          failedMessages++;
        } else {
          successfulMessages++;
        }

        if (result && result.apiCalls) {
          totalApiCalls += result.apiCalls;
        }

        totalMessagesProcessed++;

        // Log progress periodically
        if (totalMessagesProcessed % 100 === 0) {
          performanceMonitor.logProgress(`Processed ${totalMessagesProcessed} messages`, {
            successful: successfulMessages,
            failed: failedMessages,
            apiCalls: totalApiCalls,
            validationErrors: validationErrors.length,
            messageProcessingTime: Date.now() - messageStartTime
          });
        }

        logAgentActivity(agent.name, `Successfully processed event`, {
          messageId,
          result: result ? {
            successfulRecords: result.successfulRecords,
            failedRecords: result.failedRecords,
            apiCalls: result.apiCalls
          } : null
        });

        // Acknowledge message after successful processing
        channel.ack(msg);

      } catch (error) {
        logger.error(`[${agent.name}] Error processing message:`, error);
        failedMessages++;
        totalMessagesProcessed++;

        // Log processing error to performance monitor with stack trace
        performanceMonitor.logProgress(`Message processing failed`, {
          messageId,
          error: error,
          totalProcessed: totalMessagesProcessed,
          failed: failedMessages,
          errorType: error.constructor.name
        });

        // Acknowledge message even on error to prevent reprocessing
        channel.ack(msg);
      }
    };

    // Setup queue consumer
    await setupQueueConsumer(agent.queue, messageHandler);

    logAgentActivity(agent.name, `Queue listener established for ${agent.queue}`);

    // Setup graceful shutdown with performance summary
    setupGracefulShutdown(async () => {
      logAgentActivity(agent.name, 'Shutting down API outbound agent');

      // Complete performance monitoring with final summary
      const finalMetrics = await performanceMonitor.complete({
        status: 'shutdown',
        agentName: agent.name,
        totalMessagesProcessed,
        successfulMessages,
        failedMessages,
        totalApiCalls,
        validationErrors: validationErrors.length,
        errorDetails: validationErrors.slice(0, 10) // Include first 10 errors as sample
      });

      logAgentActivity(agent.name, 'Performance summary generated', {
        performanceMetrics: finalMetrics.summary
      });
    });

    // Keep process alive
    logAgentActivity(agent.name, `API outbound agent is now listening for events...`);

    // Set up periodic performance reporting (every 5 minutes)
    const reportingInterval = setInterval(() => {
      if (totalMessagesProcessed > 0) {
        performanceMonitor.logProgress(`Periodic Report`, {
          totalProcessed: totalMessagesProcessed,
          successful: successfulMessages,
          failed: failedMessages,
          successRate: ((successfulMessages / totalMessagesProcessed) * 100).toFixed(2) + '%',
          apiCalls: totalApiCalls,
          validationErrors: validationErrors.length,
          uptime: Math.round((Date.now() - Number(performanceMonitor.startTime) / 1e6) / 1000) + 's'
        });
      }
    }, 5 * 60 * 1000); // 5 minutes

    // Clear interval on shutdown and generate final summary
    const handleShutdown = async (signal) => {
      clearInterval(reportingInterval);

      // Generate final performance summary
      try {
        const finalMetrics = await performanceMonitor.complete({
          status: 'shutdown',
          agentName: agent.name,
          totalMessagesProcessed,
          successfulMessages,
          failedMessages,
          totalApiCalls,
          validationErrors: validationErrors.length,
          errorDetails: validationErrors.slice(0, 10),
          shutdownSignal: signal,
          shutdownTime: new Date().toISOString()
        });

        logAgentActivity(agent.name, 'Final performance summary generated', {
          performanceMetrics: finalMetrics.summary
        });

        // Give a moment for file operations to complete
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        logger.error('Error generating final performance summary:', error);
      }

      process.exit(0);
    };

    process.on('SIGINT', () => handleShutdown('SIGINT'));
    process.on('SIGTERM', () => handleShutdown('SIGTERM'));

    // Handle uncaught exceptions
    process.on('uncaughtException', async (error) => {
      logger.error('Uncaught Exception in outbound agent:', error);
      await handleShutdown('UNCAUGHT_EXCEPTION');
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', async (reason, promise) => {
      logger.error('Unhandled Rejection in outbound agent:', reason);
      await handleShutdown('UNHANDLED_REJECTION');
    });

  } catch (error) {
    logger.error(`[${agent.name}] Failed to setup queue listener for ${agent.queue}:`, error);
    throw error;
  }
}
// Global state for graceful shutdown
let isShuttingDown = false;
let activeProcessingPromises = new Set();
let messageCollection = [];
let collectionTimer = null;
let collectionCounter = 0;
let channel = null;
let consumerTag = null;

// Collection processing configuration
const COLLECTION_TIMEOUT = config.messageProcessing.collectionTimeout;
const SHUTDOWN_TIMEOUT = config.messageProcessing.shutdownTimeout;

// Graceful shutdown function
const gracefulShutdown = async (signal) => {
  if (isShuttingDown) {
    logger.warn(`Already shutting down, ignoring ${signal}`);
    return;
  }

  isShuttingDown = true;
  logger.info(`Received ${signal}. Starting graceful shutdown for agent: ${agent.name}...`);

  try {
    // Stop accepting new messages
    if (consumerTag && channel) {
      logger.info(`Cancelling RabbitMQ consumer for agent: ${agent.name}...`);
      await channel.cancel(consumerTag);
    }

    // Clear collection timer
    if (collectionTimer) {
      clearTimeout(collectionTimer);
      collectionTimer = null;
    }

    // Process any remaining messages in collection
    if (messageCollection.length > 0) {
      logger.info(`Processing remaining ${messageCollection.length} messages in collection for agent: ${agent.name}...`);
      await processCollection(messageCollection);
      messageCollection = [];
    }

    // Wait for active processing to complete with timeout
    logger.info(`Waiting for ${activeProcessingPromises.size} active processing operations to complete (timeout: ${SHUTDOWN_TIMEOUT}ms)...`);

    if (activeProcessingPromises.size > 0) {
      const timeoutPromise = new Promise((resolve) => setTimeout(resolve, SHUTDOWN_TIMEOUT));
      const activePromise = Promise.all(Array.from(activeProcessingPromises));

      await Promise.race([activePromise, timeoutPromise]);

      if (activeProcessingPromises.size > 0) {
        logger.warn(`Shutdown timeout reached. ${activeProcessingPromises.size} operations still active.`);
      }
    }

    // Close RabbitMQ channel
    if (channel) {
      logger.info(`Closing RabbitMQ channel for agent: ${agent.name}...`);
      await channel.close();
    }

    logger.info(`Graceful shutdown completed for agent: ${agent.name}`);
    process.exit(0);

  } catch (error) {
    logger.error(`Error during graceful shutdown for agent: ${agent.name}:`, error);
    process.exit(1);
  }
};


/**
 * Main outbound agent handler - routes to appropriate processing based on agent configuration
 * @param {Object} agent - Agent configuration
 * @returns {Promise<void>}
 */
async function outboundAgentHandler(agent) {
  const performanceMonitor = new PerformanceMonitor(`Outbound Processing - ${agent.name}`);

  try {
    logAgentActivity(agent.name, 'Starting outbound processing', {
      type: agent.type,
      source: agent.source,
      handler: agent.handler
    });

    // Validate agent configuration
    if (!validateAgentConfig(agent)) {
      throw new Error(`Invalid agent configuration for ${agent.name}`);
    }

    // Get the appropriate handler
    const handlerFunction = getHandler(agent.handler);

    performanceMonitor.startStep('Handler Execution', {
      agentName: agent.name,
      handlerName: agent.handler,
      source: agent.source
    });

    // All handlers now use collection-based queue listening approach
    // Collection-based agents run continuously, so we don't complete here

    // Setup signal handlers
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    process.on('SIGHUP', () => gracefulShutdown('SIGHUP'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error(`Uncaught Exception in agent ${agent.name}:`, error);
      gracefulShutdown('uncaughtException');
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error(`Unhandled Rejection in agent ${agent.name}:`, reason);
      gracefulShutdown('unhandledRejection');
    });

    // Function to process a complete collection
    const processCollection = async (collection) => {
      if (collection.length === 0) return;

      collectionCounter++;
      const collectionId = `collection_${collectionCounter}_${Date.now()}`;

      logger.info(`[COLLECTION ${collectionId}] Starting collection processing for agent: ${agent.name} with ${collection.length} messages`);

      try {
        // Get the appropriate collection handler based on handler type
        let collectionHandlerFunction;
        if (agent.handler === 'generateCsv') {
          collectionHandlerFunction = handlerFunction.generateCsvCollection || handlerFunction;
        } else if (agent.handler === 'sendApiData') {
          collectionHandlerFunction = handlerFunction.sendApiDataCollection || handlerFunction;
        } else if (agent.handler === 'generateXml') {
          collectionHandlerFunction = handlerFunction.generateXmlCollection || handlerFunction;
        } else {
          collectionHandlerFunction = handlerFunction;
        }

        // Process the collection
        const result = await collectionHandlerFunction({
          eventCollection: collection,
          agent,
          performanceMonitor,
          collectionId
        });

        logger.info(`[COLLECTION ${collectionId}] Collection processing completed for agent: ${agent.name}`, {
          totalEvents: result.totalEvents || collection.length,
          successfulRecords: result.successfulRecords || 0,
          failedRecords: result.failedRecords || 0,
          filesGenerated: result.filesGenerated || 0,
          apiCalls: result.apiCalls || 0
        });

        // Acknowledge all messages in the collection
        for (const { msg } of collection) {
          if (channel && !isShuttingDown) {
            channel.ack(msg);
          }
        }

      } catch (error) {
        logger.error(`[COLLECTION ${collectionId}] Error processing collection for agent: ${agent.name}:`, error);

        // Nack all messages in the collection on error
        for (const { msg } of collection) {
          if (channel && !isShuttingDown) {
            channel.nack(msg, false, false); // Don't requeue on error
          }
        }
      }
    };

    // Function to trigger collection processing
    const triggerCollectionProcessing = async () => {
      if (messageCollection.length > 0) {
        const currentCollection = [...messageCollection];
        messageCollection = []; // Clear the collection

        if (collectionTimer) {
          clearTimeout(collectionTimer);
          collectionTimer = null;
        }

        await processCollection(currentCollection);
      }
    };

    try {
      logAgentActivity(agent.name, `Setting up collection-based queue listener for: ${agent.queue}`);

      // Setup queue consumer
      await setupQueueConsumer(agent.queue, async (msg, ch) => {
        channel = ch; // Store channel reference

        if (!msg) return;

        // Don't accept new messages during shutdown
        if (isShuttingDown) {
          logger.debug(`Rejecting new message due to shutdown for agent: ${agent.name}`);
          channel.nack(msg, false, true); // requeue = true
          return;
        }

        try {
          const raw = msg.content.toString();
          let event;
          try {
            event = JSON.parse(raw);
          } catch (_e) {
            event = raw;
          }

          const messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

          // Add message to current collection
          messageCollection.push({ msg, event, messageId });

          // Set/reset timer for collection timeout
          if (collectionTimer) {
            clearTimeout(collectionTimer);
          }
          collectionTimer = setTimeout(triggerCollectionProcessing, COLLECTION_TIMEOUT);

        } catch (err) {
          logger.error(`Error adding message to collection for agent ${agent.name}:`, err);
          // Process individual message on collection error
          channel.nack(msg, false, false);
        }
      });

      logger.info(`Collection-based queue listener established for agent: ${agent.name} on queue: ${agent.queue}`);
      logger.info(`Collection timeout: ${COLLECTION_TIMEOUT}ms, Shutdown timeout: ${SHUTDOWN_TIMEOUT}ms`);

    } catch (error) {
      logger.error(`Failed to setup collection queue listener for agent ${agent.name} on queue ${agent.queue}:`, error);
      throw error;
    }

  } catch (error) {
    logger.error(`Error in outbound agent processing for ${agent.name}:`, error.message);

    performanceMonitor.complete({
      status: 'error',
      error: error.message,
      agentName: agent.name
    });

    throw error;
  }
}

module.exports = { outboundAgentHandler };
