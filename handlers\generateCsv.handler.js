const fs = require('fs');
const path = require('path');
const csv = require('fast-csv');
const logger = require('../config/logger');
const { uploadFile } = require('../services/csv.service');
const { loadMappingConfig } = require('../helpers/agent.helper');

/**
 * Extracts unique model names from mapping keys
 * @param {Object} mappingConfig - Mapping configuration object
 * @returns {Array<string>} Array of unique model names
 */
function extractModelNames(mappingConfig) {
    const modelNames = new Set();

    Object.keys(mappingConfig).forEach(key => {
        if (key.includes('.')) {
            const modelName = key.split('.')[0];
            modelNames.add(modelName);
        }
    });

    return Array.from(modelNames);
}

/**
 * Determines if mapping values are column names (strings) or numerical positions (numbers)
 * @param {Object} mappingConfig - Mapping configuration object
 * @returns {Object} Object with type ('column' or 'position') and processed mapping
 */
function analyzeMappingType(mappingConfig) {
    const values = Object.values(mappingConfig);

    // Check if all values are numbers (as strings or actual numbers)
    const allNumbers = values.every(value => {
        const num = Number(value);
        return !isNaN(num) && isFinite(num);
    });

    if (allNumbers) {
        // Convert string numbers to actual numbers and sort by position
        const sortedEntries = Object.entries(mappingConfig)
            .map(([key, value]) => [key, Number(value)])
            .sort((a, b) => a[1] - b[1]);

        return {
            type: 'position',
            mapping: Object.fromEntries(sortedEntries)
        };
    } else {
        return {
            type: 'column',
            mapping: mappingConfig
        };
    }
}

/**
 * Generic function to generate CSV data from event data using mapping configuration
 * @param {Object} params - Handler parameters
 * @param {Object} params.event - Event data from queue
 * @param {Object} params.agent - Agent configuration
 * @param {Object} params.performanceMonitor - Performance monitoring instance
 * @param {string} params.messageId - Unique message identifier
 * @returns {Promise<Object>} Result object with file info and metrics
 */
async function generateCsv({ event, agent, performanceMonitor = null, messageId }) {
    const handlerResults = {
        agentName: agent.name,
        totalRecords: 0,
        successfulRecords: 0,
        failedRecords: 0,
        filesGenerated: 0,
        errors: [],
        messageId
    };

    try {
        logger.info(`[CSV Handler] Processing event for agent: ${agent.name}`, { messageId });

        // Step 1: Load mapping configuration
        const mappingConfig = loadMappingConfig(agent.mapping);
        logger.info(`[CSV Handler] Loaded mapping configuration for: ${agent.mapping}`);

        // Step 2: Process event data
        const eventData = event.params || event.data || event;
        const batchId = require('uuid').v4();
        const context = {
            batchId,
            batchSize: Array.isArray(eventData) ? eventData.length : 1,
            timestamp: new Date().toISOString(),
            messageId
        };

        // Step 3: Analyze mapping type (column names vs positions)
        const { type: mappingType, mapping: processedMapping } = analyzeMappingType(mappingConfig);
        logger.info(`[CSV Handler] Mapping type detected: ${mappingType}`);

        // Step 4: Extract data for transformation
        let dataForTransformation = [];
        if (Array.isArray(eventData)) {
            dataForTransformation = eventData;
        } else if (eventData && typeof eventData === 'object') {
            dataForTransformation = [eventData];
        } else {
            logger.warn(`[CSV Handler] No valid data found in event for agent: ${agent.name}`, { messageId });
            return handlerResults;
        }

        handlerResults.totalRecords = dataForTransformation.length;

        if (dataForTransformation.length === 0) {
            logger.warn(`[CSV Handler] No data to process for CSV generation`, { messageId });
            return handlerResults;
        }

        // Step 5: Prepare CSV data based on mapping type
        performanceMonitor?.startStep('Transform Data', { recordCount: dataForTransformation.length });
        const csvData = [];

        if (mappingType === 'column') {
            // Column name mapping: use values as headers
            const headers = Object.values(processedMapping);
            csvData.push(headers);

            // Transform each record from event data
            dataForTransformation.forEach(record => {
                const csvRow = [];
                Object.entries(processedMapping).forEach(([modelField, csvColumn]) => {
                    // Handle nested field access (e.g., Identity.email)
                    const fieldParts = modelField.split('.');
                    let value = record;

                    for (const part of fieldParts) {
                        value = value?.[part];
                        if (value === undefined) break;
                    }

                    csvRow.push(value !== undefined ? value : '');
                });
                csvData.push(csvRow);
                handlerResults.successfulRecords++;
            });
        } else {
            // Position mapping: no headers, order by position
            dataForTransformation.forEach(record => {
                const csvRow = [];
                Object.entries(processedMapping).forEach(([modelField, position]) => {
                    // Handle nested field access (e.g., Identity.email)
                    const fieldParts = modelField.split('.');
                    let value = record;

                    for (const part of fieldParts) {
                        value = value?.[part];
                        if (value === undefined) break;
                    }

                    csvRow[position] = value !== undefined ? value : ''; // Place value at specific position
                });
                // Fill any gaps with empty strings
                for (let i = 0; i < csvRow.length; i++) {
                    if (csvRow[i] === undefined) csvRow[i] = '';
                }
                csvData.push(csvRow);
                handlerResults.successfulRecords++;
            });
        }

        performanceMonitor?.endStep('Transform Data', { transformedRows: csvData.length - (mappingType === 'column' ? 1 : 0) });

        // Step 6: Generate filename with batch information
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${agent.name}_batch_${batchId.substring(0, 8)}_${timestamp}.csv`;

        // Create temporary file for processing
        const tempDir = './downloads/temp';
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }
        const tempFilePath = path.join(tempDir, filename);

        // Step 7: Write CSV file to temporary location
        performanceMonitor?.startStep('Generate CSV File', { tempFilePath, rowCount: csvData.length, batchId });
        await new Promise((resolve, reject) => {
            const writeStream = fs.createWriteStream(tempFilePath);
            csv.write(csvData, { headers: false })
                .pipe(writeStream)
                .on('finish', resolve)
                .on('error', reject);
        });
        performanceMonitor?.endStep('Generate CSV File', { fileSize: fs.statSync(tempFilePath).size, batchId });

        const dataRows = mappingType === 'column' ? csvData.length - 1 : csvData.length;
        logger.info(`[CSV Handler] CSV file generated successfully: ${tempFilePath}`, { messageId, batchId });
        logger.info(`[CSV Handler] Generated ${dataRows} data rows from event data`, { messageId, batchId });

        // Step 8: Upload CSV file to configured destination
        performanceMonitor?.startStep('Upload CSV to Destination', {
            destination: agent.source,
            agentName: agent.name,
            fileName: filename,
            batchId
        });

        const uploadResult = await uploadFile(tempFilePath, agent, performanceMonitor, 'CSV');

        performanceMonitor?.endStep('Upload CSV to Destination', {
            success: uploadResult.success,
            destination: uploadResult.destination,
            uploadedPath: uploadResult.uploadedPath,
            error: uploadResult.error,
            batchId
        });

        // Step 9: Clean up temporary file
        try {
            fs.unlinkSync(tempFilePath);
            logger.info(`[CSV Handler] Cleaned up temporary file: ${tempFilePath}`, { messageId, batchId });
        } catch (error) {
            logger.warn(`[CSV Handler] Failed to clean up temporary file ${tempFilePath}: ${error.message}`, { messageId, batchId });
        }

        if (!uploadResult.success) {
            handlerResults.errors.push(`Failed to upload CSV to ${agent.source}: ${uploadResult.error}`);
            handlerResults.failedRecords = handlerResults.totalRecords;
            handlerResults.successfulRecords = 0;
            throw new Error(`Failed to upload CSV to ${agent.source}: ${uploadResult.error}`);
        }

        handlerResults.filesGenerated = 1;

        logger.info(`[CSV Handler] Successfully processed CSV generation for agent: ${agent.name}`, {
            messageId,
            batchId,
            totalRecords: handlerResults.totalRecords,
            successfulRecords: handlerResults.successfulRecords,
            filesGenerated: handlerResults.filesGenerated,
            fileName: filename
        });

        return handlerResults;

    } catch (error) {
        logger.error(`[CSV Handler] Error generating CSV from event data for agent: ${agent.name}:`, error.message, { messageId });
        handlerResults.errors.push(error.message);
        handlerResults.failedRecords = handlerResults.totalRecords;
        handlerResults.successfulRecords = 0;
        return handlerResults;
    }
}

/**
 * Collection-based CSV generation handler that processes multiple events in batches
 * @param {Object} params - Handler parameters
 * @param {Array} params.eventCollection - Collection of events to process
 * @param {Object} params.agent - Agent configuration
 * @param {Object} params.performanceMonitor - Performance monitoring instance
 * @param {string} params.collectionId - Unique collection identifier
 * @returns {Promise<Object>} Collection processing results
 */
async function generateCsvCollection({ eventCollection, agent, performanceMonitor = null, collectionId }) {
    const collectionResults = {
        collectionId,
        agentName: agent.name,
        totalEvents: eventCollection.length,
        totalRecords: 0,
        successfulRecords: 0,
        failedRecords: 0,
        filesGenerated: 0,
        errors: [],
        processedFiles: []
    };

    try {
        logger.info(`[CSV Collection Handler] Starting collection processing for agent: ${agent.name}`, { collectionId, eventCount: eventCollection.length });

        // Step 1: Load mapping configuration once for the entire collection
        performanceMonitor?.startStep('Load Mapping Configuration', { collectionId, agentName: agent.name });
        const mappingConfig = loadMappingConfig(agent.mapping);
        const { type: mappingType, mapping: processedMapping } = analyzeMappingType(mappingConfig);
        performanceMonitor?.endStep('Load Mapping Configuration', { mappingType, collectionId });

        logger.info(`[CSV Collection Handler] Loaded mapping configuration: ${agent.mapping} (Type: ${mappingType})`, { collectionId });

        // Step 2: Aggregate all event data from the collection
        performanceMonitor?.startStep('Aggregate Event Data', { collectionId, eventCount: eventCollection.length });
        const allEventData = [];

        for (const { event, messageId } of eventCollection) {
            const eventData = event.params || event.data || event;
            if (Array.isArray(eventData)) {
                allEventData.push(...eventData);
            } else if (eventData && typeof eventData === 'object') {
                allEventData.push(eventData);
            }
        }

        collectionResults.totalRecords = allEventData.length;
        performanceMonitor?.endStep('Aggregate Event Data', { totalRecords: allEventData.length, collectionId });

        if (allEventData.length === 0) {
            logger.warn(`[CSV Collection Handler] No data found in event collection for agent: ${agent.name}`, { collectionId });
            return collectionResults;
        }

        // Step 3: Generate single CSV file from all collected data
        const batchId = require('uuid').v4();
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${agent.name}_collection_${collectionId.substring(0, 8)}_${timestamp}.csv`;

        performanceMonitor?.startStep('Transform Collection Data', { totalRecords: allEventData.length, collectionId });
        const csvData = [];

        if (mappingType === 'column') {
            // Column name mapping: use values as headers
            const headers = Object.values(processedMapping);
            csvData.push(headers);

            // Transform each record from aggregated event data
            allEventData.forEach(record => {
                const csvRow = [];
                Object.entries(processedMapping).forEach(([modelField, csvColumn]) => {
                    // Handle nested field access (e.g., Identity.email)
                    const fieldParts = modelField.split('.');
                    let value = record;

                    for (const part of fieldParts) {
                        value = value?.[part];
                        if (value === undefined) break;
                    }

                    csvRow.push(value !== undefined ? value : '');
                });
                csvData.push(csvRow);
                collectionResults.successfulRecords++;
            });
        } else {
            // Position mapping: no headers, order by position
            allEventData.forEach(record => {
                const csvRow = [];
                Object.entries(processedMapping).forEach(([modelField, position]) => {
                    // Handle nested field access (e.g., Identity.email)
                    const fieldParts = modelField.split('.');
                    let value = record;

                    for (const part of fieldParts) {
                        value = value?.[part];
                        if (value === undefined) break;
                    }

                    csvRow[position] = value !== undefined ? value : '';
                });
                // Fill any gaps with empty strings
                for (let i = 0; i < csvRow.length; i++) {
                    if (csvRow[i] === undefined) csvRow[i] = '';
                }
                csvData.push(csvRow);
                collectionResults.successfulRecords++;
            });
        }

        performanceMonitor?.endStep('Transform Collection Data', { transformedRows: csvData.length - (mappingType === 'column' ? 1 : 0), collectionId });

        // Step 4: Generate and upload CSV file
        const tempDir = './downloads/temp';
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }
        const tempFilePath = path.join(tempDir, filename);

        performanceMonitor?.startStep('Generate Collection CSV File', { tempFilePath, rowCount: csvData.length, collectionId });
        await new Promise((resolve, reject) => {
            const writeStream = fs.createWriteStream(tempFilePath);
            csv.write(csvData, { headers: false })
                .pipe(writeStream)
                .on('finish', resolve)
                .on('error', reject);
        });
        performanceMonitor?.endStep('Generate Collection CSV File', { fileSize: fs.statSync(tempFilePath).size, collectionId });

        const dataRows = mappingType === 'column' ? csvData.length - 1 : csvData.length;
        logger.info(`[CSV Collection Handler] Generated collection CSV file: ${tempFilePath}`, { collectionId, dataRows });

        // Step 5: Upload file to destination
        performanceMonitor?.startStep('Upload Collection CSV', { destination: agent.source, collectionId });
        const uploadResult = await uploadFile(tempFilePath, agent, performanceMonitor, 'CSV');
        performanceMonitor?.endStep('Upload Collection CSV', { success: uploadResult.success, collectionId });

        // Step 6: Clean up temporary file
        try {
            fs.unlinkSync(tempFilePath);
            logger.info(`[CSV Collection Handler] Cleaned up temporary file: ${tempFilePath}`, { collectionId });
        } catch (error) {
            logger.warn(`[CSV Collection Handler] Failed to clean up temporary file ${tempFilePath}: ${error.message}`, { collectionId });
        }

        if (!uploadResult.success) {
            collectionResults.errors.push(`Failed to upload CSV to ${agent.source}: ${uploadResult.error}`);
            collectionResults.failedRecords = collectionResults.totalRecords;
            collectionResults.successfulRecords = 0;
        } else {
            collectionResults.filesGenerated = 1;
            collectionResults.processedFiles.push({
                filename,
                uploadPath: uploadResult.uploadedPath,
                recordCount: dataRows
            });
        }

        logger.info(`[CSV Collection Handler] Collection processing completed for agent: ${agent.name}`, {
            collectionId,
            totalEvents: collectionResults.totalEvents,
            totalRecords: collectionResults.totalRecords,
            successfulRecords: collectionResults.successfulRecords,
            filesGenerated: collectionResults.filesGenerated
        });

        return collectionResults;

    } catch (error) {
        logger.error(`[CSV Collection Handler] Error processing collection for agent: ${agent.name}:`, error.message, { collectionId });
        collectionResults.errors.push(error.message);
        collectionResults.failedRecords = collectionResults.totalRecords;
        collectionResults.successfulRecords = 0;
        return collectionResults;
    }
}

module.exports = { generateCsv, generateCsvCollection };
